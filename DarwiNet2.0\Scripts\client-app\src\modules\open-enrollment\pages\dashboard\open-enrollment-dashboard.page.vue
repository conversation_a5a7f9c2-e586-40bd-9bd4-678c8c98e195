﻿<script setup lang="ts">
import EnrollmentTopNavigation from "@open-enrollment/components/enrollment-top-navigation.vue";
import {useGetEnrollmentProgress} from "@open-enrollment/composables/use-get-enrollment-progress.ts";
import dayjs from "dayjs";
import EnrollmentBottomNavigation from "@open-enrollment/components/enrollment-bottom-navigation.vue";
import {computed} from "vue";

const {data: enrollmentProgress} = useGetEnrollmentProgress()
const enrollmentEndDate = computed(() => !!enrollmentProgress.value && !!enrollmentProgress.value.EnrollmentEndDate 
    ? dayjs(enrollmentProgress.value.EnrollmentEndDate).format('MMM DD, YYYY')
    : 'N/A')
</script>

<template>
  <div class="w-full h-[100vh] relative bg-white flex flex-col">
    <main class="flex flex-col h-full gap-6">
      <!-- Header -->
      <div class="sticky top-0 z-40">
        <EnrollmentTopNavigation/>

        <!-- Banner  -->
        <section class="container mx-auto px-4 py-6 sm:px-6 lg:px-8" aria-labelledby="oe-banner-title">
          <div class="rounded-2xl bg-brand-teal/5 shadow-sm ring-1 ring-black/5 overflow-hidden space-y-2">
            <div class="flex items-center justify-between px-8 py-4 my-3 mx-2">
              <div class="flex flex-col gap-2">
                <h2 id="oe-banner-title" class="text-xl sm:text-2xl font-bold text-neutral-900">
                  Open Enrollment is Here!
                </h2>
                <p class="text-sm text-neutral-700">
                  Follow the enrollment wizard by clicking the button below to complete your Open Enrollment.
                </p>
              </div>
              <p class="font-bold">
                <span class="text-sm">Your Enrollment End Date is:</span> <br>
                <span class="text-2xl">{{ enrollmentEndDate }}</span>
              </p>
            </div>
          </div>
        </section>
      </div>

      <!-- Previous Year Selection -->
      <section class="container mx-auto px-4 py-6 sm:px-6 lg:px-8" aria-labelledby="previous-year-elections">
        <h3 id="previous-year-elections" class="text-md sm:text-2xl font-semibold text-neutral-900">
          Previous Year Elections
        </h3>
        <!-- TODO: need previous year elections -->
      </section>

    </main>

    <EnrollmentBottomNavigation/>
  </div>
</template>