<script setup lang="ts">
import {<PERSON>, CardContent, CardDescription, CardFooter, Card<PERSON>eader, CardTitle} from '@components/ui/card'
import {Skeleton} from '@components/ui/skeleton'
import NumberFlow from '@number-flow/vue'
import {type EmployeeEligibility, useGetOpenEnrollmentSummary} from '@open-enrollment/api'
import BenefitSummary from '@open-enrollment/components/benefit-summary.vue'
import SignElectionsDialog from '@open-enrollment/components/sign-elections-dialog.vue'
import {computed, ref} from 'vue'
import {useGetEmployeeBenefits} from "@open-enrollment/composables/use-get-employee-benefits.ts";

const props = defineProps<{
  employeeEligibility: EmployeeEligibility
}>()

const emit = defineEmits<{
  navigateToBenefit: [benefitType: string]
}>()

// Composable
const {isLoading, isFetching, data: employeeBenefits} = useGetEmployeeBenefits()
const {data: benefitsSummary, status: benefitsSummaryStatus} = useGetOpenEnrollmentSummary({
  SelectedYear: props.employeeEligibility.SelectedYear,
  OeEffectiveDate: props.employeeEligibility.OeEffectiveDate,
  OeTransactionId: props.employeeEligibility.TransactionId,
  EeType: props.employeeEligibility.EnrollmentType,
})

// State
const isAnimating = ref(false)
const currentStep = ref<number | undefined>(undefined)
const showSignElectionsDialog = ref(false)

// Computed 
const isAllBenefitsCompleted = computed(() => {
  if (!employeeBenefits?.value) return false

  return employeeBenefits?.value?.every(benefit =>
      benefit.Status === 'Waived' || benefit.Status === 'Completed'
  )
})


// Method
const handleStepClick = (stepIndex: number, benefitType: string) => {
  currentStep.value = stepIndex
  emit('navigateToBenefit', benefitType)
}

const numberOfCompletedBenefits = computed(() => benefitsSummary.value?.PlansCount)
</script>

<template>
  <Card class="border-border bg-card shadow-lg flex flex-col h-full max-h-[650px]">
    <CardHeader class="gap-1">
      <CardTitle class="text-lg font-bold">Benefits</CardTitle>
      <CardDescription v-if="!isFetching || !isLoading">
        Completed {{ numberOfCompletedBenefits }}/{{ employeeBenefits?.length }}
      </CardDescription>
    </CardHeader>

    <div class="px-4">
      <div class="border-top-light-gray"/>
    </div>

    <!-- CONTENT -->
    <CardContent class="flex-1 min-h-0 overflow-y-auto no-scrollbar p-0">
      <!-- Loading UI -->
      <div v-if="isLoading" class="space-y-6 animate-in fade-in-50 duration-500 px-2 py-1">
        <!-- Enrollment Progress Skeleton -->
        <div class="space-y-3">
          <div class="flex items-center gap-2">
            <Skeleton class="h-4 w-4 rounded-full"/>
            <Skeleton class="h-4 w-32"/>
          </div>
          <div class="space-y-2">
            <div
                v-for="i in 4"
                :key="i"
                class="border-l-4 border-l-border animate-in slide-in-from-left-4 duration-300"
                :style="{ animationDelay: `${i * 150}ms` }"
            >
              <div class="flex items-center gap-3 p-3">
                <!-- Stepper Indicator Skeleton -->
                <Skeleton class="w-6 h-6 rounded-full"/>

                <!-- Benefit Summary Content Skeleton -->
                <div class="flex-1 space-y-2">
                  <div class="flex items-center justify-between">
                    <Skeleton class="h-5 w-32"/>
                    <Skeleton class="h-5 w-16 rounded-full"/>
                  </div>
                  <div class="space-y-1">
                    <Skeleton class="h-3 w-full"/>
                    <Skeleton class="h-3 w-3/4"/>
                  </div>
                  <div class="flex items-center gap-2">
                    <Skeleton class="h-3 w-20"/>
                    <Skeleton class="h-3 w-24"/>
                  </div>
                </div>
                <Skeleton class="w-4 h-4"/>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Error UI -->
      <div v-else-if="benefitsSummaryStatus === 'error'"
           class="text-center py-8 animate-in fade-in-50 duration-500 px-3">
        <div class="mx-auto max-w-md">
          <div class="card-error rounded-lg p-6">
            <div class="flex items-center justify-center w-12 h-12 mx-auto mb-4 bg-error/20 rounded-full">
              <svg class="w-6 h-6 text-error" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                />
              </svg>
            </div>
            <h3 class="text-subtitle font-semibold text-error mb-2">Error Loading Summary</h3>
            <p class="text-error/80 text-sm">Unable to load enrollment summary. Please try again.</p>
          </div>
        </div>
      </div>

      <!-- List -->
      <div v-else class="animate-in fade-in-50 duration-500 space-y-2 px-2 py-1">
        <div
            v-for="(benefit, index) in employeeBenefits"
            :key="`${benefit.BenefitType}-${index}`"
            :class="{
              'transition-all duration-300 cursor-pointer group animate-in slide-in-from-left-4 rounded-md': true,
              'border-1 border-brand-teal bg-brand-teal/10 shadow-md': currentStep === index + 1,
              'border-none hover:border-brand-light-gray hover:bg-muted/40': currentStep !== index + 1
            }"
            :style="{ animationDelay: `${index * 100}ms` }"
            @click="() => handleStepClick(index + 1, benefit.BenefitType)"
        >
          <BenefitSummary
              v-if="!!benefitsSummary"
              :benefit="benefit"
              :benefit-summary="benefitsSummary"
              :is-selected="currentStep === index + 1"
          />
        </div>
      </div>
    </CardContent>

    <!-- Cost Breakdown -->
    <CardFooter class="flex-shrink-0 py-3 px-6 gap-2">
      <div
          class="space-y-3 mt-3 pt-2 flex justify-between items-center animate-in slide-in-from-bottom-4 duration-500 border-top-light-gray w-full"
          style="animation-delay: 400ms;"
      >
        <p class="text-sm font-semibold">Cost Breakdown</p>
        <div class="flex items-center gap-2">
          <div class="font-semibold text-base transition-colors duration-200" :class="{ 'text-success': isAnimating }">
            <NumberFlow
                :value="benefitsSummary?.EePerMonthTotalPerMonthTotal ?? 0"
                :format="{ style: 'currency', currency: 'USD' }"
                suffix="/mo"
                @animationsstart="isAnimating = true"
                @animationsfinish="isAnimating = false"
            />
          </div>
          <span class="text-muted-foreground"> | </span>
          <span class="text-sm mt-0.5 transition-colors duration-200" :class="{ 'text-success': isAnimating }">
            <NumberFlow
                :value="benefitsSummary?.EePerCheckTotal ?? 0"
                :format="{ style: 'currency', currency: 'USD' }"
                suffix="/check"
                @animationsstart="isAnimating = true"
                @animationsfinish="isAnimating = false"
            />
          </span>
        </div>
      </div>
    </CardFooter>

    <!-- Dialogs -->
    <SignElectionsDialog
        :open="showSignElectionsDialog"
        :is-all-benefits-completed="isAllBenefitsCompleted"
        :transaction-id="employeeEligibility!.TransactionId"
        :year="employeeEligibility!.SelectedYear"
        @update:open="showSignElectionsDialog = $event"
    />
    <!--
    <WaiveBenefitDialog
      :open="showWaiveAllPlansDialog"
      :type="'waive-all'"
      :year="employeeEligibility!.SelectedYear"
      :transaction-id="employeeEligibility!.TransactionId"
      :oe-effective-date="employeeEligibility!.OeEffectiveDate"
      @update:open="showWaiveAllPlansDialog = $event"
    />
    -->
  </Card>
</template>
