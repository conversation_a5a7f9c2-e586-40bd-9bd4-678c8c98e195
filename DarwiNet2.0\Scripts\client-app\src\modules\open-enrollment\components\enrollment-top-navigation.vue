﻿<script setup lang="ts">
import {useGetEnrollmentProgress} from "@open-enrollment/composables/use-get-enrollment-progress.ts";
import {useEnrollmentNavigation} from "@open-enrollment/composables/use-enrollment-navigation.ts";

// Composable
const {data: enrollmentProgress} = useGetEnrollmentProgress()
const {stepper, onNavigate, isCurrentLocation} = useEnrollmentNavigation()

</script>

<template>
  <div class="w-full bg-white">
    <!-- Top nav -->
    <nav
        v-if="!!enrollmentProgress"
        aria-label="Enrollment steps"
        class="w-full flex flex-col"
    >
      <!-- Step navigation bar -->
      <div class="w-full border-b border-gray-100 bg-white">
        <div class="mx-auto max-w-screen-xl px-3 sm:px-6 lg:px-8 py-3">
          <div class="flex flex-wrap items-center justify-center gap-4">
            <button
                v-for="step in stepper.steps.value"
                :key="step.title"
                type="button"
                :aria-current="isCurrentLocation(step.route) ? 'step' : undefined"
                :class="[
                  'flex items-center gap-1.5 sm:gap-2 whitespace-nowrap rounded-full outline outline-1 transition-colors',
                  'focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:ring-0',
                  isCurrentLocation(step.route)
                    ? 'bg-teal-600/5 outline-teal-600'
                    : 'outline-stone-300 hover:bg-gray-50',
                    'px-2.5 py-1.5 text-[11px] sm:px-3 sm:py-2 sm:text-xs md:px-3.5 md:py-2 md:text-sm'
                ]"
                @click="onNavigate(step.route)"
            >
              <component
                  :is="step.icon"
                  class="h-3.5 w-3.5 sm:h-4 sm:w-4"
                  :class="[
                  isCurrentLocation(step.route) ? 'text-teal-700' : 'text-neutral-600'
                ]"
                  aria-hidden="true"
              />
              <span
                  :class="[
                  isCurrentLocation(step.route)
                    ? 'text-teal-500 font-medium'
                    : 'text-gray-600 font-normal'
                ]"
              >
                {{ step.title }}
              </span>
            </button>
          </div>
        </div>
      </div>

      <!-- Enrollment progress -->
      <div
          class="relative w-full bg-neutral-300 border-y border-neutral-300 h-5 px-5"
          role="progressbar"
          :aria-valuenow="enrollmentProgress.PercentageDone"
          aria-valuemin="0"
          aria-valuemax="100"
          aria-label="Enrollment progress"
      >
        <!-- Track (filled) -->
        <div
            class="absolute inset-y-0 left-0 bg-brand-green transition-[width] duration-300 ease-out"
            :style="{ width: enrollmentProgress.PercentageDone + '%' }"
        />

        <!-- Text on top (dark over light) -->
        <div class="absolute inset-0 flex items-center pointer-events-none">
          <span class="pl-2 text-[11px] font-bold leading-none text-neutral-700">
            {{ enrollmentProgress.PercentageDone }}% Enrollment Progress
          </span>
        </div>

        <!-- Text inside the filled bar (white over green) -->
        <div
            class="absolute inset-0 overflow-hidden pointer-events-none"
            :style="{ width: enrollmentProgress.PercentageDone + '%' }"
        >
          <div class="h-full flex items-center">
            <span class="pl-2 text-[11px] font-bold leading-none text-white">
              {{ enrollmentProgress.PercentageDone }}% Enrollment Progress
            </span>
          </div>
        </div>
      </div>
    </nav>
  </div>
</template>