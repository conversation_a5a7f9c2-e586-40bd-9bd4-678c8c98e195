﻿<script setup lang="ts">
import EnrollmentTopNavigation from "@open-enrollment/components/enrollment-top-navigation.vue";
import {useEmployeeEligibility} from "@open-enrollment/composables/use-employee-eligibility.ts";
import {But<PERSON>} from "@components/ui/button"
import {<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Trash} from "lucide-vue-next";
import {useGetDependents} from "@open-enrollment/composables/use-get-dependents.ts";
import {formatSSN} from "@open-enrollment/utils";
import dayjs from "dayjs";
import DependentFormDialog from "@open-enrollment/components/dependent/dependent-form-dialog.vue";
import {ref} from "vue";
import type {DependentResponse} from "@open-enrollment/types/open-enrollment.types.ts";
import DeleteDependentDialog from "@open-enrollment/components/dependent/delete-dependent-dialog.vue";
import EnrollmentBottomNavigation from "@open-enrollment/components/enrollment-bottom-navigation.vue";

// Composable
const {data} = useEmployeeEligibility()
const {data: dependents} = useGetDependents()

// States
const dependentDialog = ref<{
  mode: "add" | "edit" | "delete" | undefined
  selectedDependent?: DependentResponse
  opened: boolean
}>()

// Methods
const openAddDependentDialog = () => dependentDialog.value = {
  mode: 'add',
  opened: true
}
const openEditDependentDialog = (dependentId: number) => {
  const selectedDependent = dependents.value?.find(i => i.ID === dependentId)
  if (selectedDependent) {
    dependentDialog.value = {
      mode: 'edit',
      selectedDependent,
      opened: true
    }
  }
}
const openDeleteDependentDialog = (dependentId: number) => {
  const selectedDependent = dependents.value?.find(i => i.ID === dependentId)
  if (selectedDependent) {
    dependentDialog.value = {
      mode: 'delete',
      selectedDependent,
      opened: true
    }
  }
}

const closeDialog = () => dependentDialog.value = {
  mode: undefined,
  opened: false
}
</script>

<template>
  <Toaster :rich-colors="true"/>

  <div class="w-full h-[100vh] relative bg-white flex flex-col">
    <main v-if="!!data" class="flex flex-col h-full gap-6">
      <!-- Header -->
      <div class="sticky top-0 z-40">
        <EnrollmentTopNavigation/>

        <!-- Banner  -->
        <section class="container mx-auto px-4 py-6 sm:px-6 lg:px-8" aria-labelledby="oe-banner-title">
          <div class="rounded-2xl bg-brand-teal/5 shadow-sm ring-1 ring-black/5 overflow-hidden space-y-2">
            <!-- Content -->
            <div class="flex items-center justify-between px-8 py-4">
              <div class="space-y-2">
                <h2 id="oe-banner-title" class="text-xl sm:text-2xl font-bold text-neutral-900">
                  Eligible Dependents
                </h2>
                <p class="text-sm text-neutral-700">
                  Your dependents may also be eligible, including your spouse, domestic partner, and children up to
                  the
                  age 26.
                </p>
              </div>

              <Button variant="outline" @click="openAddDependentDialog">
                Add a New Dependent
              </Button>
            </div>

            <!--  Footer-->
            <div class="bg-warning/50">
              <div class="px-4 sm:px-6 md:px-8 py-0">
                <p class="flex text-center text-[13px] sm:text-sm font-medium text-black gap-2">
                  <span class="flex items-center gap-1 font-bold"><TriangleAlert class="icon-sm"/> Warning: </span>
                  <span>Updating dependents while choosing benefits will restart the enrollment! (It will wipe out all current
                  selections)</span>
                </p>
              </div>
            </div>
          </div>
        </section>
      </div>

      <!-- Dependents -->
      <section class="flex-1 overflow-y-auto container mx-auto px-4 py-6 sm:px-6 lg:px-8">
        <h3 class="text-lg sm:text-xl font-semibold text-neutral-900">Added Dependents</h3>

        <!-- Header row (md+) -->
        <div class="mt-3 hidden md:grid grid-cols-12 items-center px-4 text-xs font-semibold text-neutral-600">
          <div class="col-span-3">Full Name</div>
          <div class="col-span-3">SSN</div>
          <div class="col-span-1">Gender</div>
          <div class="col-span-2">Date of Birth</div>
          <div class="col-span-2">Relation</div>
          <div class="col-span-1"></div>
        </div>

        <!-- Rows -->
        <div v-if="!!dependents" class="mt-2 space-y-3">
          <article
              v-for="d in dependents"
              :key="d.ID"
              class="rounded-md bg-white shadow-sm ring-1 ring-black/5">
            <div class="grid grid-cols-1 md:grid-cols-12 gap-3 md:gap-4 items-center px-4 py-3">
              <!-- Full Name -->
              <div class="md:col-span-3">
                <p class="md:hidden text-[11px] uppercase tracking-wide text-neutral-500">Full Name</p>
                <p class="font-semibold text-neutral-900">{{ d.FirstName }} {{ d.LastName }}</p>
              </div>
              <!-- SSN -->
              <div class="md:col-span-3">
                <p class="md:hidden text-[11px] uppercase tracking-wide text-neutral-500">SSN</p>
                <p class="text-neutral-800">{{ formatSSN(d.SocialSecurity) }}</p>
              </div>
              <!-- Gender -->
              <div class="md:col-span-1">
                <p class="md:hidden text-[11px] uppercase tracking-wide text-neutral-500">Gender</p>
                <p class="text-neutral-800">{{ d.Gender }}</p>
              </div>
              <!-- DoB -->
              <div class="md:col-span-2">
                <p class="md:hidden text-[11px] uppercase tracking-wide text-neutral-500">Date of Birth</p>
                <p class="text-neutral-800">{{ dayjs(d.BirthDate).format("MMMM D, YYYY") }}</p>
              </div>
              <!-- Relation -->
              <div class="md:col-span-2">
                <p class="md:hidden text-[11px] uppercase tracking-wide text-neutral-500">Relation</p>
                <p class="text-neutral-800">{{ d.EmployeeRelationshipText }}</p>
              </div>
              <!-- Actions -->
              <div class="md:col-span-1">
                <p class="md:hidden text-[11px] uppercase tracking-wide text-neutral-500"></p>
                <div class="flex items-center justify-end gap-2">
                  <Pencil class="icon-sm text-brand-green" @click="openEditDependentDialog(d.ID)"/>
                  <Trash class="icon-sm text-error" @click="openDeleteDependentDialog(d.ID)"/>
                </div>
              </div>
            </div>
          </article>
        </div>
      </section>

      <!-- Dependent Dialog -->
      <DependentFormDialog
          v-if="dependentDialog && dependentDialog.mode === 'add'"
          :opened="dependentDialog.opened"
          :mode="dependentDialog.mode"
          @update:open="closeDialog"/>
      <DependentFormDialog
          v-if="dependentDialog && dependentDialog.mode === 'edit'"
          :opened="dependentDialog.opened"
          :dependent="dependentDialog.selectedDependent!"
          :mode="dependentDialog.mode"
          @update:open="closeDialog"/>
      <DeleteDependentDialog
          v-if="dependentDialog && dependentDialog.mode === 'delete'"
          :open="dependentDialog.opened"
          :dependent="dependentDialog.selectedDependent!"
          @update:open="closeDialog"/>
    </main>

    <EnrollmentBottomNavigation/>
  </div>
</template>