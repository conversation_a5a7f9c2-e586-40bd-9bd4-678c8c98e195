import {useStepper} from "@vueuse/core";
import {FileCheck2, House, LayoutGrid, Users} from "lucide-vue-next";

export const useEnrollmentNavigation = () => {
    const steps = {
        'get-started': {
            id: 'get-started',
            title: 'Get Started',
            icon: House,
            route: '/OpenEnrollment/Dashboard'
        },
        'dependents': {
            id: 'dependents',
            title: 'Manage Dependents',
            icon: Users,
            route: '/OpenEnrollment/Dependents'
        },
        'benefits': {
            id: 'benefits',
            title: 'Choose Benefits',
            icon: LayoutGrid,
            route: '/OpenEnrollment/Benefits'
        },
        'summary': {
            id: 'summary',
            title: 'Benefits Summary',
            icon: FileCheck2,
            route: '/OpenEnrollment/Summary'
        },
    }
    const stepper = useStepper(steps)

    // Methods
    const onNavigate = (location: string) => window.location.href = location
    const isCurrentLocation = (path: string) => window.location.pathname.includes(path)

    return {stepper, onNavigate, isCurrentLocation}
}