﻿<script setup lang="ts">
import {useEnrollmentNavigation} from "@open-enrollment/composables/use-enrollment-navigation.ts";
import {Button} from "@/components/ui/button"

const {stepper} = useEnrollmentNavigation()
</script>

<template>
  <div class="sticky bottom-5 z-100">
    <section class="container rounded-xl border shadow-xl mx-auto px-4 py-6" aria-labelledby="oe-bottom-navigation">
      <div class="flex items-center justify-between">
        <Button v-show="!stepper.isFirst.value">
          Previous
        </Button>
        <div>
          <Button v-show="stepper.isCurrent('benefits')">
            Verify & Sign
          </Button>
          <Button v-show="stepper.isCurrent('get-started') || stepper.isCurrent('dependents')">
            Continue
          </Button>
        </div>
      </div>
    </section>
  </div>
</template>
