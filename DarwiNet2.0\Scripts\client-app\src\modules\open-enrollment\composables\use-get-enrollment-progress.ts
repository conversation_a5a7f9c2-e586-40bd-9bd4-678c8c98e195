﻿import {openEnrollmentApiKeys} from "@open-enrollment/composables/open-enrollment.hooks.ts";
import {getEnrollmentProgress} from "@open-enrollment/api/open-enrollment.apis.ts";
import {useQuery} from "@tanstack/vue-query";
import {useEmployeeEligibility} from "@open-enrollment/composables/use-employee-eligibility.ts";
import {computed} from "vue";

/**
 * Hook to get enrollment progress for the employee
 * @returns The enrollment progress with employee enrollments data
 */
export const useGetEnrollmentProgress = () => {
    const {data: employeeEligibility} = useEmployeeEligibility()

    return useQuery({
        queryKey: openEnrollmentApiKeys.enrollmentProgress(employeeEligibility.value?.SelectedYear ?? 0, employeeEligibility.value?.TransactionId ?? 0),
        queryFn: () => employeeEligibility.value && getEnrollmentProgress(employeeEligibility.value!.SelectedYear, employeeEligibility.value!.TransactionId),
        staleTime: 1000 * 60 * 5, // 5 minutes
        enabled: computed(() => !!employeeEligibility.value),
    })
}
