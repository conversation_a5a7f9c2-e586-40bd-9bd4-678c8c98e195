﻿<script setup lang="ts">
import EnrollmentTopNavigation from "@open-enrollment/components/enrollment-top-navigation.vue";
import {Toaster} from "vue-sonner";
import {useEmployeeEligibility} from "@open-enrollment/composables/use-employee-eligibility.ts";
import {But<PERSON>} from "@components/ui/button"
import {X} from "lucide-vue-next";
import {ref} from "vue";
import PlanSection from "@open-enrollment/components/plan-section.vue";
import EnrollmentSummary from "@open-enrollment/components/enrollment-summary.vue";
import RestartEnrollmentDialog from "@open-enrollment/components/restart-enrollment-dialog.vue";
import WaiveBenefitDialog from "@open-enrollment/components/waive-benefit-dialog.vue";
import EnrollmentBottomNavigation from "@open-enrollment/components/enrollment-bottom-navigation.vue";

// Composable
const {data: employeeEligibility} = useEmployeeEligibility()

// State
const activeBenefitType = ref<string | null>(null)
const waiveAllBenefitDialogOpened = ref(false)

// Method
const handleStepperNavigation = (benefitType: string) => activeBenefitType.value = benefitType
</script>

<template>
  <Toaster :rich-colors="true"/>

  <div class="w-full h-[100vh] relative bg-white flex flex-col">
    <main v-if="!!employeeEligibility" class="flex flex-col h-full">
      <!-- STICKY HEADER: Enrollment Header + Banner -->
      <div class="sticky top-0 z-40 bg-white shadow-sm">
        <!-- Enrollment Header -->
        <EnrollmentTopNavigation/>

        <!-- Dashboard Banner  -->
        <section class="w-full" aria-labelledby="oe-banner-title">
          <div class="bg-brand-teal/5 space-y-2 px-4">
            <!-- Content -->
            <div class="flex items-center justify-between px-8 py-4">
              <div class="space-y-2">
                <h2 id="oe-banner-title" class="text-xl sm:text-2xl font-bold text-neutral-900">
                  Benefits Selection
                </h2>
              </div>

              <div class="flex items-center justify-between gap-2">
                <RestartEnrollmentDialog :employee-eligibility="employeeEligibility"/>

                <WaiveBenefitDialog
                    :open="waiveAllBenefitDialogOpened"
                    :type="'waive-all'"
                    @update:open="waiveAllBenefitDialogOpened = $event"/>

                <Button variant="outline" @click="() => {
                  waiveAllBenefitDialogOpened = true
                }">
                  <X class="icon-sm"/>
                  Waive Remaining Plans
                </Button>
              </div>
            </div>
          </div>
        </section>
      </div>

      <!-- SCROLLABLE CONTENT: Benefits -->
      <section class="flex-1 overflow-y-auto">
        <div class="container mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div class="grid gap-6 lg:grid-cols-12">
            <!-- PLAN DETAILS (left) -->
            <div class="order-2 lg:order-1 lg:col-span-8">
              <PlanSection :active-benefit-type="activeBenefitType"/>
            </div>

            <!-- SUMMARY (right) -->
            <aside class="h-[700px] order-1 lg:order-2 lg:col-span-4 lg:sticky lg:top-6">

              <EnrollmentSummary
                  :employee-eligibility="employeeEligibility"
                  @navigate-to-benefit="handleStepperNavigation"
              />
            </aside>
          </div>
        </div>
      </section>
    </main>

    <EnrollmentBottomNavigation/>
  </div>
</template>
